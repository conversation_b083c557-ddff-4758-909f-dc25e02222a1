import { NavigationContainer, useNavigationContainerRef } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import { useColorScheme, Platform } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { ErrorBoundary } from '@components';
import { AuthProvider, ThemeProvider, useThemePreferences, getEffectiveTheme } from '@contexts';
import AppNavigator from '@navigation/AppNavigator';
import { createTheme } from '@theme/variants';
import NavigationStateDebugger from './components/NavigationStateDebugger';
import '@i18n/index';

// Test if basic React Native components work
console.log('App.tsx loaded successfully');

// Import the new utilities and hooks
import { useTabVisibility } from './hooks/usePageVisibility';
import {
  loadNavigationState,
  saveNavigationState,
  shouldRestoreNavigationState,
  isValidNavigationState,
  webNavigationUtils,
  debugNavigationState,
  NAVIGATION_PERSISTENCE_KEY
} from './utils/webNavigation';

// Inner app component that uses theme context
const AppContent: React.FC = () => {
  const { preferences, isLoading } = useThemePreferences();
  const systemColorScheme = useColorScheme();
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const [initialNavigationState, setInitialNavigationState] = useState<any>(null);

  const navigationRef = useNavigationContainerRef();
  const { isTabActive } = useTabVisibility();
  const currentStateRef = useRef<any>(null);
  const isRestoringRef = useRef(false);

  const isDark = getEffectiveTheme(preferences, systemColorScheme) === 'dark';

  // Memoize theme creation to prevent unnecessary re-creations
  const theme = useMemo(() => {
    return createTheme(preferences, isDark);
  }, [preferences, isDark]);

  // Function to get current navigation state
  const getCurrentNavigationState = useCallback(() => {
    if (navigationRef.isReady()) {
      return navigationRef.getRootState();
    }
    return currentStateRef.current;
  }, [navigationRef]);

  // Load navigation state on app start - SYNCHRONOUSLY
  useEffect(() => {
    const restoreState = async () => {
      try {
        if (!shouldRestoreNavigationState()) {
          console.log('📱 [APP] Skipping navigation state restoration');
          setIsNavigationReady(true);
          return;
        }

        console.log('🔄 [APP] Starting synchronous state restoration...');

        // Load state synchronously on web using sessionStorage
        let state = null;
        if (Platform.OS === 'web' && typeof sessionStorage !== 'undefined') {
          try {
            const stateString = sessionStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
            console.log(`🔄 [APP] Checking sessionStorage for key: ${NAVIGATION_PERSISTENCE_KEY}`);
            console.log(`🔄 [APP] Found state string: ${stateString ? 'YES' : 'NO'}`);
            if (stateString) {
              state = JSON.parse(stateString);
              console.log('🔄 [APP] ✅ Loaded state synchronously from sessionStorage');
              debugNavigationState(state, 'SYNC_RESTORE');

              // Set the state immediately for NavigationContainer
              console.log('🔄 [APP] Setting initial state IMMEDIATELY');
              setInitialNavigationState(state);
              currentStateRef.current = state;
              isRestoringRef.current = true;
            } else {
              console.log('🔄 [APP] ❌ No state found in sessionStorage');
            }
          } catch (syncError) {
            console.warn('🔄 [APP] ❌ Synchronous load failed:', syncError);
          }
        }

        // Fallback to async loading if sync failed
        if (!state) {
          console.log('🔄 [APP] Falling back to async loading...');
          state = await loadNavigationState();

          // Additional fallback to localStorage on web
          if (!state && Platform.OS === 'web') {
            state = webNavigationUtils.getStateFromLocalStorage();
          }
        }

        // Only set state if we haven't already set it synchronously
        if (state && isValidNavigationState(state) && !isRestoringRef.current) {
          console.log('🔄 [APP] Restoring navigation state (async fallback)');
          debugNavigationState(state, 'ASYNC_RESTORE');
          console.log('🔄 [APP] Setting initial navigation state for NavigationContainer (async)');
          isRestoringRef.current = true;
          setInitialNavigationState(state);
          currentStateRef.current = state;

          // Log what we're setting as initial state
          const mainRoute = state.routes?.[state.index];
          if (mainRoute?.name === 'Main' && mainRoute.state) {
            const tabState = mainRoute.state;
            const activeTab = tabState.routes?.[tabState.index];
            console.log(`🔄 [APP] Initial state will have active tab: ${activeTab?.name} (index: ${tabState.index})`);
          }
        } else if (isRestoringRef.current) {
          console.log('🔄 [APP] State already set synchronously, skipping async restore');
        } else {
          console.log('📱 [APP] No valid navigation state to restore');
          if (state) {
            debugNavigationState(state, 'INVALID_STATE');
          }
        }
      } catch (error) {
        console.warn('❌ [APP] Failed to restore navigation state:', error);
      } finally {
        setIsNavigationReady(true);
      }
    };

    if (!isNavigationReady) {
      restoreState();
    }
  }, [isNavigationReady]);

  // Setup web-specific navigation handlers
  useEffect(() => {
    if (Platform.OS !== 'web') return;

    const cleanupBrowserNav = webNavigationUtils.setupBrowserNavigation();
    const cleanupPageUnload = webNavigationUtils.setupPageUnloadHandler(getCurrentNavigationState);

    return () => {
      cleanupBrowserNav();
      cleanupPageUnload();
    };
  }, [getCurrentNavigationState]);

  // Handle tab visibility changes to prevent unnecessary resets
  useEffect(() => {
    if (Platform.OS === 'web') {
      const currentState = getCurrentNavigationState();
      if (!isTabActive) {
        // Save current state when tab becomes inactive
        console.log('🔄 [APP] Tab became inactive, saving current state...');
        if (currentState) {
          debugNavigationState(currentState, 'TAB_INACTIVE_SAVE');
          console.log('🔄 [APP] Triggering state save from tab inactive');
          saveNavigationState(currentState);
        }
      } else {
        // Tab became active
        console.log('🔄 [APP] Tab became active, current state:');
        if (currentState) {
          debugNavigationState(currentState, 'TAB_ACTIVE_CURRENT');
        }
      }
    }
  }, [isTabActive, getCurrentNavigationState]);

  if (isLoading || !isNavigationReady) {
    return null;
  }

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer
        key="main-navigation"
        ref={navigationRef}
        initialState={initialNavigationState}
        onReady={() => {
          console.log('🚀 [APP] Navigation container ready');

          // Get the actual current state from the navigation container
          const actualCurrentState = navigationRef.getRootState();
          console.log('🚀 [APP] Actual current state in NavigationContainer:');
          debugNavigationState(actualCurrentState, 'ACTUAL_CONTAINER_STATE');

          if (initialNavigationState) {
            console.log('🔄 [APP] Navigation container ready with restored state');
            console.log('🔄 [APP] Expected initial state was:');
            debugNavigationState(initialNavigationState, 'EXPECTED_INITIAL_STATE');

            // Compare expected vs actual
            const expectedMainRoute = initialNavigationState.routes?.[initialNavigationState.index];
            const actualMainRoute = actualCurrentState?.routes?.[actualCurrentState.index];

            let expectedTab = 'None';
            let actualTab = 'None';

            if (expectedMainRoute?.name === 'Main' && expectedMainRoute.state) {
              const tabState = expectedMainRoute.state;
              expectedTab = tabState.routes?.[tabState.index]?.name || 'None';
            }

            if (actualMainRoute?.name === 'Main' && actualMainRoute.state) {
              const tabState = actualMainRoute.state;
              if (typeof tabState.index === 'number' && tabState.routes) {
                actualTab = tabState.routes[tabState.index]?.name || 'None';
              }
            }

            console.log(`🔄 [APP] Expected tab: ${expectedTab}, Actual tab: ${actualTab}`);

            if (expectedTab !== actualTab && expectedTab !== 'None') {
              console.log(`🔄 [APP] State mismatch! Forcing navigation to expected tab: ${expectedTab}`);
              setTimeout(() => {
                navigationRef.navigate(expectedTab as never);
              }, 100);
            }

            // Reset restoration flag after navigation is complete
            setTimeout(() => {
              isRestoringRef.current = false;
              console.log('🔄 [APP] Restoration complete, resuming normal state saving');
            }, 300);
          } else {
            console.log('🔄 [APP] Navigation container ready with default state');
          }
          setIsNavigationReady(true);
        }}
        onStateChange={(state) => {
          if (state) {
            debugNavigationState(state, 'STATE_CHANGE');
            currentStateRef.current = state;

            // Don't save state during restoration to prevent overwriting
            if (!isRestoringRef.current) {
              console.log('🔄 [APP] Triggering state save from onStateChange');
              // Save state asynchronously to avoid blocking navigation
              saveNavigationState(state).catch(error => {
                console.warn('🔄 [APP] ❌ Failed to save navigation state:', error);
              });
            } else {
              console.log('🔄 [APP] Skipping state save during restoration');
            }
          }
        }}
      >
        <ErrorBoundary>
          <AppNavigator />
        </ErrorBoundary>

        {/* Navigation State Debugger - shows on all screens in development */}
        <NavigationStateDebugger />
      </NavigationContainer>
      <StatusBar style={isDark ? "light" : "dark"} />
    </PaperProvider>
  );
};

const App: React.FC = () => {
  console.log('🎉 Full Kibbutz App Loading...');

  if (typeof document !== 'undefined') {
    document.title = 'Kibbutz App';
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <AuthProvider>
          <ThemeProvider>
            <AppContent />
          </ThemeProvider>
        </AuthProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
};

export default App;
