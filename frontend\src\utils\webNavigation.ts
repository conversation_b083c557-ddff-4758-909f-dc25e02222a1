import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Web-specific navigation utilities to handle browser tab switching
 * and navigation state persistence
 */

export const NAVIGATION_PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';
export const NAVIGATION_READY_KEY = 'NAVIGATION_READY_V1';

/**
 * Safely save navigation state to storage
 */
export const saveNavigationState = async (state: any): Promise<void> => {
  try {
    if (!state) {
      console.warn('⚠️ Attempted to save null/undefined navigation state');
      return;
    }

    // Extract detailed state information for logging
    const mainRoute = state.routes?.[state.index];
    let activeTab = 'None';
    let tabIndex = -1;
    if (mainRoute?.name === 'Main' && mainRoute.state) {
      const tabState = mainRoute.state;
      const tab = tabState.routes?.[tabState.index];
      activeTab = tab?.name || 'None';
      tabIndex = tabState.index;
    }

    const timestamp = Date.now();
    const stateString = JSON.stringify(state);

    console.log(`💾 [SAVE] Starting save operation`);
    console.log(`💾 [SAVE] Active tab: ${activeTab} (index: ${tabIndex})`);
    console.log(`💾 [SAVE] Main route: ${mainRoute?.name}`);
    console.log(`💾 [SAVE] State size: ${stateString.length} chars`);
    console.log(`💾 [SAVE] Timestamp: ${timestamp}`);

    // Save to AsyncStorage (React Native standard)
    await AsyncStorage.setItem(NAVIGATION_PERSISTENCE_KEY, stateString);
    await AsyncStorage.setItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`, timestamp.toString());
    console.log(`💾 [SAVE] ✅ AsyncStorage saved`);

    // Save to sessionStorage on web (better for tab-specific state)
    if (Platform.OS === 'web' && typeof sessionStorage !== 'undefined') {
      try {
        sessionStorage.setItem(NAVIGATION_PERSISTENCE_KEY, stateString);
        sessionStorage.setItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`, timestamp.toString());
        console.log(`💾 [SAVE] ✅ sessionStorage saved`);
      } catch (sessionError) {
        console.warn('💾 [SAVE] ❌ sessionStorage failed:', sessionError);
      }
    }

    // Also save to localStorage as fallback
    if (Platform.OS === 'web' && typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(`${NAVIGATION_PERSISTENCE_KEY}_fallback`, stateString);
        localStorage.setItem(`${NAVIGATION_PERSISTENCE_KEY}_fallback_timestamp`, timestamp.toString());
        console.log(`💾 [SAVE] ✅ localStorage fallback saved`);
      } catch (localError) {
        console.warn('💾 [SAVE] ❌ localStorage fallback failed:', localError);
      }
    }

    console.log(`💾 [SAVE] ✅ Save operation completed successfully`);
  } catch (error) {
    console.error('💾 [SAVE] ❌ Save operation failed:', error);
  }
};

/**
 * Safely load navigation state from storage
 */
export const loadNavigationState = async (): Promise<any | null> => {
  try {
    console.log(`📱 [LOAD] Starting load operation`);

    let stateString = null;
    let source = 'none';
    let timestamp = null;

    // Priority 1: sessionStorage (best for tab-specific state)
    if (Platform.OS === 'web' && typeof sessionStorage !== 'undefined') {
      stateString = sessionStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
      if (stateString) {
        source = 'sessionStorage';
        timestamp = sessionStorage.getItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`);
        console.log(`📱 [LOAD] Found state in sessionStorage`);
      }
    }

    // Priority 2: AsyncStorage (React Native standard)
    if (!stateString) {
      stateString = await AsyncStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
      if (stateString) {
        source = 'AsyncStorage';
        timestamp = await AsyncStorage.getItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`);
        console.log(`📱 [LOAD] Found state in AsyncStorage`);
      }
    }

    // Priority 3: localStorage fallback
    if (!stateString && Platform.OS === 'web' && typeof localStorage !== 'undefined') {
      stateString = localStorage.getItem(`${NAVIGATION_PERSISTENCE_KEY}_fallback`);
      if (stateString) {
        source = 'localStorage_fallback';
        timestamp = localStorage.getItem(`${NAVIGATION_PERSISTENCE_KEY}_fallback_timestamp`);
        console.log(`📱 [LOAD] Found state in localStorage fallback`);
      }
    }

    if (!stateString) {
      console.log(`📱 [LOAD] ❌ No saved navigation state found in any storage`);
      return null;
    }

    const state = JSON.parse(stateString);

    // Extract detailed state information for logging
    const mainRoute = state.routes?.[state.index];
    let activeTab = 'None';
    let tabIndex = -1;
    if (mainRoute?.name === 'Main' && mainRoute.state) {
      const tabState = mainRoute.state;
      const tab = tabState.routes?.[tabState.index];
      activeTab = tab?.name || 'None';
      tabIndex = tabState.index;
    }

    console.log(`📱 [LOAD] Source: ${source}`);
    console.log(`📱 [LOAD] Active tab: ${activeTab} (index: ${tabIndex})`);
    console.log(`📱 [LOAD] Main route: ${mainRoute?.name}`);
    console.log(`📱 [LOAD] State size: ${stateString.length} chars`);
    console.log(`📱 [LOAD] Saved timestamp: ${timestamp}`);

    // Check if state is valid and not too old
    if (timestamp) {
      const savedTime = parseInt(timestamp, 10);
      const now = Date.now();
      const age = now - savedTime;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      console.log(`📱 [LOAD] State age: ${Math.round(age / 1000)}s (max: ${Math.round(maxAge / 1000)}s)`);

      if (age > maxAge) {
        console.log(`📱 [LOAD] ⏰ State is too old, clearing and ignoring`);
        await clearNavigationState();
        return null;
      }
    }

    console.log(`📱 [LOAD] ✅ Load operation completed successfully`);
    return state;
  } catch (error) {
    console.error(`📱 [LOAD] ❌ Load operation failed:`, error);
    return null;
  }
};

/**
 * Clear saved navigation state
 */
export const clearNavigationState = async (): Promise<void> => {
  try {
    console.log(`🧹 [CLEAR] Starting clear operation`);

    // Clear AsyncStorage
    await AsyncStorage.removeItem(NAVIGATION_PERSISTENCE_KEY);
    await AsyncStorage.removeItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`);
    await AsyncStorage.removeItem(NAVIGATION_READY_KEY);
    console.log(`🧹 [CLEAR] ✅ AsyncStorage cleared`);

    // Clear sessionStorage on web
    if (Platform.OS === 'web' && typeof sessionStorage !== 'undefined') {
      try {
        sessionStorage.removeItem(NAVIGATION_PERSISTENCE_KEY);
        sessionStorage.removeItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`);
        console.log(`🧹 [CLEAR] ✅ sessionStorage cleared`);
      } catch (sessionError) {
        console.warn(`🧹 [CLEAR] ❌ sessionStorage clear failed:`, sessionError);
      }
    }

    // Clear localStorage fallback on web
    if (Platform.OS === 'web' && typeof localStorage !== 'undefined') {
      try {
        localStorage.removeItem(`${NAVIGATION_PERSISTENCE_KEY}_fallback`);
        localStorage.removeItem(`${NAVIGATION_PERSISTENCE_KEY}_fallback_timestamp`);
        console.log(`🧹 [CLEAR] ✅ localStorage fallback cleared`);
      } catch (localError) {
        console.warn(`🧹 [CLEAR] ❌ localStorage clear failed:`, localError);
      }
    }

    console.log(`🧹 [CLEAR] ✅ Clear operation completed successfully`);
  } catch (error) {
    console.error(`🧹 [CLEAR] ❌ Clear operation failed:`, error);
  }
};

/**
 * Check if navigation state should be restored based on platform and conditions
 */
export const shouldRestoreNavigationState = (): boolean => {
  // Always restore on web to prevent tab switching issues
  if (Platform.OS === 'web') {
    return true;
  }

  // On mobile, restore only if app was backgrounded recently
  return true;
};

/**
 * Debug function to log navigation state details
 */
export const debugNavigationState = (state: any, context: string): void => {
  if (!state) {
    console.log(`🔍 [${context}] Navigation state is null/undefined`);
    return;
  }

  const currentRoute = state.routes?.[state.index];
  let activeTab = null;

  // If we're on Main stack, get the active tab info
  if (currentRoute?.name === 'Main' && currentRoute.state) {
    const tabState = currentRoute.state;
    activeTab = tabState.routes?.[tabState.index];
  }

  console.log(`🔍 [${context}] Navigation state:`, {
    routesCount: state.routes?.length || 0,
    currentIndex: state.index,
    currentRoute: currentRoute?.name,
    activeTab: activeTab?.name || 'None',
    tabIndex: currentRoute?.state?.index,
    fullState: state,
  });
};

/**
 * Validate navigation state structure
 */
export const isValidNavigationState = (state: any): boolean => {
  if (!state || typeof state !== 'object') {
    return false;
  }

  // Basic validation - check for required navigation state properties
  if (!state.routes || !Array.isArray(state.routes) || state.routes.length === 0) {
    return false;
  }

  // Check if state has valid structure
  if (typeof state.index !== 'number' || state.index < 0 || state.index >= state.routes.length) {
    return false;
  }

  return true;
};

/**
 * Web-specific navigation state handling
 */
export const webNavigationUtils = {
  /**
   * Handle browser back/forward navigation
   */
  setupBrowserNavigation: () => {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return () => {}; // No-op cleanup function
    }

    const handlePopState = (event: PopStateEvent) => {
      console.log('🔄 Browser navigation detected:', event.state);
      // Handle browser back/forward if needed
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  },

  /**
   * Handle page unload to save state
   */
  setupPageUnloadHandler: (getCurrentState: () => any) => {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return () => {}; // No-op cleanup function
    }

    const handleBeforeUnload = () => {
      const currentState = getCurrentState();
      if (currentState) {
        // Use synchronous storage for page unload
        try {
          localStorage.setItem(NAVIGATION_PERSISTENCE_KEY, JSON.stringify(currentState));
        } catch (error) {
          console.warn('Failed to save state on page unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  },

  /**
   * Get state from localStorage as fallback
   */
  getStateFromLocalStorage: (): any | null => {
    if (Platform.OS !== 'web' || typeof localStorage === 'undefined') {
      return null;
    }

    try {
      const stateString = localStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
      return stateString ? JSON.parse(stateString) : null;
    } catch (error) {
      console.warn('Failed to get state from localStorage:', error);
      return null;
    }
  },
};
