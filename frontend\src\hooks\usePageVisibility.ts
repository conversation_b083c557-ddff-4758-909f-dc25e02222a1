import { useEffect, useState } from 'react';
import { Platform } from 'react-native';

/**
 * Custom hook to track page visibility using the Page Visibility API
 * This helps prevent navigation state reset when switching browser tabs
 * Only works on web platform
 */
export const usePageVisibility = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Only run on web platform
    if (Platform.OS !== 'web' || typeof document === 'undefined') {
      return;
    }

    // Check if Page Visibility API is supported
    const supported = 'visibilityState' in document;
    setIsSupported(supported);

    if (!supported) {
      console.warn('Page Visibility API not supported in this browser');
      return;
    }

    // Set initial visibility state
    setIsVisible(document.visibilityState === 'visible');

    // Handle visibility change events
    const handleVisibilityChange = () => {
      const visible = document.visibilityState === 'visible';
      console.log('🔄 Page visibility changed:', visible ? 'visible' : 'hidden');
      setIsVisible(visible);
    };

    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return {
    isVisible,
    isSupported,
    isWeb: Platform.OS === 'web',
  };
};

/**
 * Custom hook to handle window focus/blur events
 * Provides additional layer of detection for tab switching
 */
export const useWindowFocus = () => {
  const [isFocused, setIsFocused] = useState(true);

  useEffect(() => {
    // Only run on web platform
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return;
    }

    // Set initial focus state
    setIsFocused(document.hasFocus());

    const handleFocus = () => {
      console.log('🔄 Window focused');
      setIsFocused(true);
    };

    const handleBlur = () => {
      console.log('🔄 Window blurred');
      setIsFocused(false);
    };

    // Add event listeners
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    // Cleanup
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, []);

  return {
    isFocused,
    isWeb: Platform.OS === 'web',
  };
};

/**
 * Combined hook that uses both Page Visibility API and window focus events
 * for comprehensive tab switching detection
 */
export const useTabVisibility = () => {
  const { isVisible, isSupported: pageVisibilitySupported } = usePageVisibility();
  const { isFocused } = useWindowFocus();

  // Use Page Visibility API if supported, otherwise fall back to window focus
  const isTabActive = pageVisibilitySupported ? isVisible : isFocused;

  return {
    isTabActive,
    isVisible,
    isFocused,
    pageVisibilitySupported,
    isWeb: Platform.OS === 'web',
  };
};
