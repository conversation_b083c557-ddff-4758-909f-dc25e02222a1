// API Service for Custom Backend Communication
import { API_BASE_URL } from '../lib/supabase';

interface ApiResponse<T> {
  data: T | null;
  error: string | null;
}

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  private token: string | null = null;

  setToken(token: string | null) {
    this.token = token;
    console.log('🎫 API service token updated:', !!token);
  }

  private async request(endpoint: string, options: RequestInit & { skipAuth?: boolean } = {}): Promise<ApiResponse<any>> {
    const { skipAuth, ...requestOptions } = options;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(requestOptions.headers as Record<string, string> || {}),
    };

    // Add auth header if not skipping auth and we have a token
    if (!skipAuth && this.token) {
      console.log('🎫 Using stored token for API request');
      headers['Authorization'] = `Bearer ${this.token}`;
    } else if (!skipAuth && !this.token) {
      console.log('❌ No token available for API request');
      return {
        data: null,
        error: 'Authentication required'
      };
    }

    console.log('🌐 Making API request to:', `${this.baseUrl}${endpoint}`);
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...requestOptions,
        headers,
      });
      console.log('📡 API response received:', response.status);

      const data = await response.json();

      if (!response.ok) {
        return {
          data: null,
          error: data.error || 'Request failed'
        };
      }

      return {
        data: data.data || data, // Handle both wrapped and direct responses
        error: null
      };
    } catch (error) {
      console.error('API request error:', error);
      return {
        data: null,
        error: 'Network error'
      };
    }
  }

  // Event API methods
  async getEvents(): Promise<ApiResponse<any[]>> {
    return this.request('/api/events');
  }

  async getEvent(id: string): Promise<ApiResponse<any>> {
    return this.request(`/api/events/${id}`);
  }

  async createEvent(eventData: any): Promise<ApiResponse<any>> {
    return this.request('/api/events', {
      method: 'POST',
      body: JSON.stringify(eventData),
    });
  }

  async updateEvent(id: string, eventData: any): Promise<ApiResponse<any>> {
    return this.request(`/api/events/${id}`, {
      method: 'PUT',
      body: JSON.stringify(eventData),
    });
  }

  async deleteEvent(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/events/${id}`, {
      method: 'DELETE',
    });
  }

  // Food API methods
  async getFoodItems(): Promise<ApiResponse<any[]>> {
    return this.request('/api/food');
  }

  async createFoodItem(foodData: any): Promise<ApiResponse<any>> {
    return this.request('/api/food', {
      method: 'POST',
      body: JSON.stringify(foodData),
    });
  }

  // Job API methods
  async getJobs(): Promise<ApiResponse<any[]>> {
    return this.request('/api/jobs');
  }

  async createJob(jobData: any): Promise<ApiResponse<any>> {
    return this.request('/api/jobs', {
      method: 'POST',
      body: JSON.stringify(jobData),
    });
  }

  // User API methods
  async getUserProfile(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/api/users/${userId}`);
  }

  async createUserProfile(userData: any): Promise<ApiResponse<any>> {
    return this.request('/api/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUserProfile(userId: string, userData: any): Promise<ApiResponse<any>> {
    return this.request(`/api/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async getAllUsers(): Promise<ApiResponse<any[]>> {
    return this.request('/api/users');
  }

  async getPeople(): Promise<ApiResponse<any[]>> {
    return this.request('/api/users/people');
  }

  // Auth API methods
  async register(userData: any): Promise<ApiResponse<any>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials: any): Promise<ApiResponse<any>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request('/health');
  }

  // Debug API methods (admin only)
  async getDebugTables(): Promise<ApiResponse<{ tables: string[] }>> {
    return this.request('/api/debug/tables');
  }

  async getDebugTableEntries(tableName: string, limit: number = 100): Promise<ApiResponse<{ entries: any[], total: number }>> {
    return this.request(`/api/debug/tables/${tableName}/entries?limit=${limit}`);
  }

  // Chat API methods
  async getChatGroups(): Promise<ApiResponse<any[]>> {
    return this.request('/api/chat/groups');
  }

  async getChatGroup(groupId: string): Promise<ApiResponse<any>> {
    return this.request(`/api/chat/groups/${groupId}`);
  }

  async getChatMessages(groupId: string): Promise<ApiResponse<any[]>> {
    return this.request(`/api/chat/groups/${groupId}/messages`);
  }

  async sendMessage(groupId: string, message: any): Promise<ApiResponse<any>> {
    // Note: groupId is kept for API consistency but the message object already contains group_id
    // The message should already be formatted with snake_case fields by the chat service
    console.log('🌐 API: Sending message to /api/chat/messages');
    console.log('📋 API: Message payload:', message);

    return this.request('/api/chat/messages', {
      method: 'POST',
      body: JSON.stringify(message),
    });
  }

  async createChatGroup(groupData: any): Promise<ApiResponse<any>> {
    return this.request('/api/chat/groups', {
      method: 'POST',
      body: JSON.stringify(groupData),
    });
  }
}

export const apiService = new ApiService();
export default apiService;
